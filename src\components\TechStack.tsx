import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

const TechStack = () => {
  const techCategories = [
    {
      category: "Frontend",
      icon: "🎨",
      color: "text-terminal-cyan",
      technologies: [
        { name: "React", level: 95 },
        { name: "Next.js", level: 95 },
        { name: "TypeScript", level: 92 },
        { name: "Tailwind CSS", level: 95 },
        { name: "Vue.js", level: 85 },
      ],
    },
    {
      category: "Backend",
      icon: "⚙️",
      color: "text-terminal-green",
      technologies: [
        { name: "Node.js", level: 93 },
        { name: "Python", level: 85 },
        { name: "Express.js", level: 90 },
        { name: "PostgreSQL", level: 87 },
        { name: "MongoDB", level: 85 },
      ],
    },
    {
      category: "DevOps & Cloud",
      icon: "☁️",
      color: "text-terminal-purple",
      technologies: [
        { name: "<PERSON><PERSON>", level: 90 },
        { name: "<PERSON><PERSON>", level: 92 },
        { name: "Kubernetes", level: 85 },
        { name: "CI/CD", level: 88 },
        { name: "Terraform", level: 80 },
      ],
    },
    {
      category: "Tools & Others",
      icon: "🛠️",
      color: "text-orange-400",
      technologies: [
        { name: "Git", level: 95 },
        { name: "Linux", level: 90 },
        { name: "Postman", level: 85 },
        { name: "Webpack", level: 82 },
        { name: "GraphQL", level: 80 },
      ],
    },
  ];

  return (
    <section id="tech" className="py-24 relative">
      <div className="container mx-auto px-6">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold mb-4 text-center gradient-text">
            $ ls -la skills/
          </h2>
          <p className="text-center text-terminal-text mb-12">
            Technologies I work with to build amazing solutions
          </p>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {techCategories.map((category, index) => (
              <Card
                key={category.category}
                className="glass-effect border-terminal-border hover:neon-border transition-all duration-300 group hover:scale-105"
              >
                <CardContent className="p-6">
                  <div className="text-center mb-6">
                    <div className="text-4xl mb-3 group-hover:animate-bounce">
                      {category.icon}
                    </div>
                    <h3 className={`text-xl font-bold ${category.color} mb-2`}>
                      {category.category}
                    </h3>
                  </div>

                  <div className="space-y-4">
                    {category.technologies.map((tech) => (
                      <div key={tech.name} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-terminal-text">
                            {tech.name}
                          </span>
                          <span className="text-xs text-terminal-cyan">
                            {tech.level}%
                          </span>
                        </div>
                        <div className="w-full bg-terminal-border rounded-full h-2">
                          <div
                            className="h-2 rounded-full bg-gradient-to-r from-terminal-cyan to-terminal-purple transition-all duration-1000 ease-out"
                            style={{
                              width: `${tech.level}%`,
                              boxShadow: "0 0 10px rgba(0, 212, 255, 0.5)",
                            }}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Additional Skills */}
          <div className="mt-12 text-center">
            <h3 className="text-xl font-bold text-terminal-cyan mb-6">
              Also experienced with
            </h3>
            <div className="flex flex-wrap justify-center gap-3">
              {[
                "Redis",
                "Nginx",
                "Jenkins",
                "Ansible",
                "AWS",
                "Grafana",
                "Elasticsearch",
                "RabbitMQ",
                "Microservices",
                "Serverless",
                "Firebase",
                "Supabase",
                "Stripe API",
                "WebRTC",
                "Socket.io",
              ].map((tech) => (
                <Badge
                  key={tech}
                  variant="outline"
                  className="border-terminal-border text-terminal-text hover:border-terminal-cyan hover:text-terminal-cyan transition-colors cursor-default"
                >
                  {tech}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TechStack;
