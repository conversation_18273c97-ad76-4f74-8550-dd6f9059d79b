import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Mail, MapPin, Clock, Send } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import emailjs from "@emailjs/browser";

const ContactForm = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      await emailjs.send(
        import.meta.env.VITE_EMAILJS_SERVICE_ID,
        import.meta.env.VITE_EMAILJS_TEMPLATE_ID,
        {
          from_name: formData.name,
          from_email: formData.email,
          subject: formData.subject,
          message: formData.message,
        },
        import.meta.env.VITE_EMAILJS_PUBLIC_KEY
      );

      toast({
        title: "Message sent successfully! 🚀",
        description:
          "Thanks for reaching out. I'll get back to you within 24 hours.",
      });

      setFormData({ name: "", email: "", subject: "", message: "" });
    } catch (error) {
      toast({
        title: "Something went wrong! ❌",
        description: "Please try again later or email directly.",
        variant: "destructive",
      });
      console.error("EmailJS error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section id="contact" className="py-24 relative">
      <div className="container mx-auto px-6">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold mb-4 text-center gradient-text">
            $ contact --method=all
          </h2>
          <p className="text-center text-terminal-text mb-12">
            Ready to start your next project? Let's connect and build something
            amazing together.
          </p>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* Contact Info */}
            <div className="lg:col-span-1 space-y-6">
              <Card className="glass-effect border-terminal-border">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-terminal-cyan mb-6">
                    Get in Touch
                  </h3>

                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <Mail className="w-5 h-5 text-terminal-green" />
                      <div>
                        <div className="text-sm text-terminal-text">Email</div>
                        <div className="text-terminal-cyan">
                          <EMAIL>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3">
                      <MapPin className="w-5 h-5 text-terminal-green" />
                      <div>
                        <div className="text-sm text-terminal-text">
                          Location
                        </div>
                        <div className="text-terminal-cyan">
                          Available Worldwide (Remote)
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3">
                      <Clock className="w-5 h-5 text-terminal-green" />
                      <div>
                        <div className="text-sm text-terminal-text">
                          Response Time
                        </div>
                        <div className="text-terminal-cyan">
                          Within 24 hours
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="mt-6 pt-6 border-t border-terminal-border">
                    <div className="text-sm text-terminal-text mb-3">
                      Current Status
                    </div>
                    <div className="flex flex-wrap gap-2">
                      <Badge className="bg-terminal-green/20 text-terminal-green">
                        🟢 Available
                      </Badge>
                      <Badge className="bg-terminal-cyan/20 text-terminal-cyan">
                        💼 Taking Projects
                      </Badge>
                      <Badge className="bg-terminal-purple/20 text-terminal-purple">
                        📅 Open for Internships
                      </Badge>
                      <Badge className="bg-terminal-green/20 text-terminal-green">
                        📢 Open for Consultations
                      </Badge>
                      <Badge className="bg-terminal-cyan/20 text-terminal-cyan">
                        📢 Open for Freelancing
                      </Badge>
                      <Badge className="bg-terminal-purple/20 text-terminal-purple">
                        📢 Open for Hackathon Collaboration
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Contact Options */}
              <Card className="glass-effect border-terminal-border">
                <CardContent className="p-6">
                  <h4 className="text-lg font-bold text-terminal-cyan mb-4">
                    Quick Contact
                  </h4>
                  <div className="space-y-3">
                    <Button
                      className="w-full bg-terminal-cyan text-terminal-bg hover:bg-terminal-cyan/80"
                      onClick={() =>
                        window.open("mailto:<EMAIL>", "_blank")
                      }
                    >
                      <Mail className="w-4 h-4 mr-2" />
                      Send Email
                    </Button>
                    <Button
                      variant="outline"
                      className="w-full border-terminal-green text-terminal-green hover:bg-terminal-green hover:text-terminal-bg"
                      onClick={() =>
                        window.open(
                          "https://calendly.com/pritish9801-edu/30min",
                          "_blank"
                        )
                      }
                    >
                      Schedule Call
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Contact Form */}
            <div className="lg:col-span-2">
              <Card className="glass-effect border-terminal-border">
                <CardContent className="p-8">
                  <div className="mb-6">
                    <h3 className="text-xl font-bold text-terminal-cyan mb-2">
                      Send a Message
                    </h3>
                    <p className="text-terminal-text">
                      Tell me about your project and let's discuss how we can
                      work together.
                    </p>
                  </div>

                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-terminal-text mb-2">
                          Name *
                        </label>
                        <Input
                          name="name"
                          value={formData.name}
                          onChange={handleInputChange}
                          required
                          className="bg-terminal-border border-terminal-border text-terminal-text focus:border-terminal-cyan"
                          placeholder="Your Name"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-terminal-text mb-2">
                          Email *
                        </label>
                        <Input
                          type="email"
                          name="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          required
                          className="bg-terminal-border border-terminal-border text-terminal-text focus:border-terminal-cyan"
                          placeholder="<EMAIL>"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-terminal-text mb-2">
                        Subject *
                      </label>
                      <Input
                        name="subject"
                        value={formData.subject}
                        onChange={handleInputChange}
                        required
                        className="bg-terminal-border border-terminal-border text-terminal-text focus:border-terminal-cyan"
                        placeholder="Project Discussion / Consultation / etc."
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-terminal-text mb-2">
                        Message *
                      </label>
                      <Textarea
                        name="message"
                        value={formData.message}
                        onChange={handleInputChange}
                        required
                        rows={6}
                        className="bg-terminal-border border-terminal-border text-terminal-text focus:border-terminal-cyan resize-none"
                        placeholder="Tell me about your project, timeline, budget, and any specific requirements..."
                      />
                    </div>

                    <Button
                      type="submit"
                      disabled={isSubmitting}
                      className="w-full bg-terminal-cyan text-terminal-bg hover:bg-terminal-cyan/80 font-bold py-3 disabled:opacity-50"
                    >
                      {isSubmitting ? (
                        <div className="flex items-center">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-terminal-bg mr-2"></div>
                          Sending Message...
                        </div>
                      ) : (
                        <div className="flex items-center">
                          <Send className="w-4 h-4 mr-2" />
                          Send Message
                        </div>
                      )}
                    </Button>
                  </form>

                  <div className="mt-6 p-4 bg-terminal-border/50 rounded-lg">
                    <div className="text-sm text-terminal-text">
                      <span className="text-terminal-green">💡 Pro tip:</span>{" "}
                      Include details about your project scope, timeline, and
                      budget for a faster and more accurate response.
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactForm;
