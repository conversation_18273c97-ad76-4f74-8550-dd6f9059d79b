import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import HeroSection from "@/components/HeroSection";
import TechStack from "@/components/TechStack";
import ProjectsSection from "@/components/ProjectsSection";
import ContactForm from "@/components/ContactForm";
import {
  Github,
  ExternalLink,
  Mail,
  Calendar,
  MapPin,
  Clock,
  Linkedin,
} from "lucide-react";

const Index = () => {
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  return (
    <div className="min-h-screen bg-terminal-bg">
      {/* Animated background particles */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        {Array.from({ length: 50 }).map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-terminal-cyan opacity-20 rounded-full animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 3}s`,
            }}
          />
        ))}
      </div>

      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 glass-effect">
        <div className="container mx-auto px-6 py-4">
          <div className="flex justify-between items-center">
            <div className="text-terminal-cyan font-bold text-xl">
              &lt;pritish.dev /&gt;
            </div>
            <div className="hidden md:flex space-x-8">
              {["about", "tech", "projects", "contact"].map((item) => (
                <a
                  key={item}
                  href={`#${item}`}
                  className="text-terminal-text hover:text-terminal-cyan transition-colors duration-300 capitalize"
                >
                  {item}
                </a>
              ))}
            </div>
            <div className="flex items-center space-x-2 text-sm text-terminal-text">
              <Clock className="w-4 h-4" />
              <span>{currentTime.toLocaleTimeString()}</span>
              <Badge
                variant="outline"
                className="text-terminal-green border-terminal-green"
              >
                Available Worldwide
              </Badge>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <HeroSection />

      <section id="about" className="py-24 relative">
        <div className="container mx-auto px-6">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-4xl font-bold mb-12 text-center gradient-text">
              $ whoami
            </h2>

            <div className="grid md:grid-cols-[35%_65%] gap-12 items-center">
              {/* Left: 3D Profile Image */}
              <section className="relative flex items-center justify-center min-h-[70vh] bg-[#0a0a0f] px-4 py-12">
                {/* Neon Square */}
                <div className="relative w-72 h-72 border-2 border-terminal-cyan rounded-xl shadow-[0_0_25px_#00d4ff88]">
                  {/* Popping image */}
                  <img
                    src="/my-pic-bg.png"
                    alt="Pritish Biswas"
                    className="absolute -top-9 left-1/2 -translate-x-1/2 scale-125 drop-shadow-[0_8px_30px_rgba(0,212,255,0.3)]"
                  />
                </div>
              </section>

              {/* Right: About Card */}
              <Card className="glass-effect border-terminal-border">
                <CardContent className="p-8">
                  <div className="space-y-6">
                    <div>
                      <div className="text-terminal-cyan mb-2">// About me</div>
                      <p className="text-terminal-text leading-relaxed mb-4">
                        Experienced in Next.js, TailwindCSS, MERN Stack, and
                        modern web technologies, building scalable,
                        high-performance applications with seamless UX. Proven
                        team leader, having led teams to 10+ hackathons,
                        delivering impactful solutions.
                      </p>
                      <p className="text-terminal-text leading-relaxed">
                        Exploring DevOps & Cloud with Docker, Kubernetes, CI/CD,
                        AWS, GCP. Passionate about AI-driven solutions and
                        real-time analytics. Continuously learning to push
                        boundaries with cutting-edge tech.
                      </p>
                    </div>

                    <div className="flex flex-wrap gap-3">
                      {[
                        "Problem Solver",
                        "System Architect",
                        "Code Craftsman",
                        "Cloud Wrangler",
                        "AI Tinkerer",
                      ].map((trait) => (
                        <Badge
                          key={trait}
                          variant="secondary"
                          className="bg-terminal-border text-terminal-cyan"
                        >
                          {trait}
                        </Badge>
                      ))}
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mt-6">
                      <div className="glass-effect p-6 rounded-lg text-center">
                        <div className="text-terminal-green mb-2">
                          Experience
                        </div>
                        <div className="text-2xl font-bold text-terminal-cyan">
                          2+ Years
                        </div>
                        <div className="text-sm text-terminal-text">
                          Building & Scaling
                        </div>
                      </div>
                      <div className="glass-effect p-6 rounded-lg text-center">
                        <div className="text-terminal-green mb-2">Projects</div>
                        <div className="text-2xl font-bold text-terminal-cyan">
                          20+
                        </div>
                        <div className="text-sm text-terminal-text">
                          Delivered Successfully
                        </div>
                      </div>
                      <div className="glass-effect p-6 rounded-lg text-center">
                        <div className="text-terminal-green mb-2">
                          Availability
                        </div>
                        <div className="text-2xl font-bold text-terminal-cyan">
                          24/7
                        </div>
                        <div className="text-sm text-terminal-text">
                          Global Timezone
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Tech Stack Section */}
      <TechStack />

      {/* Projects Section */}
      <ProjectsSection />

      {/* Why Hire Me Section */}
      <section className="py-24 relative">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl font-bold mb-8 text-center gradient-text">
              $ cat why_hire_me.md
            </h2>
            <div className="grid md:grid-cols-2 gap-6">
              {[
                {
                  title: "Full Stack Versatility",
                  description:
                    "From React frontends to Node.js backends, I handle the complete development lifecycle.",
                  icon: "🎯",
                },
                {
                  title: "DevOps & Cloud Expertise",
                  description:
                    "AWS, Docker, Kubernetes, CI/CD - I ensure your applications scale and deploy smoothly.",
                  icon: "☁️",
                },
                {
                  title: "Global Availability",
                  description:
                    "Available across all time zones with excellent communication and project management.",
                  icon: "🌍",
                },
                {
                  title: "Modern Practices",
                  description:
                    "Clean code, testing, documentation, and agile methodologies are my standard approach.",
                  icon: "⚡",
                },
              ].map((item, index) => (
                <Card
                  key={index}
                  className="glass-effect border-terminal-border hover:neon-border transition-all duration-300"
                >
                  <CardContent className="p-6">
                    <div className="text-3xl mb-4">{item.icon}</div>
                    <h3 className="text-xl font-bold text-terminal-cyan mb-3">
                      {item.title}
                    </h3>
                    <p className="text-terminal-text">{item.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-24 relative">
        <div className="container mx-auto px-6">
          <div className="max-w-2xl mx-auto text-center">
            <Card className="glass-effect border-terminal-border">
              <CardContent className="p-8">
                <h2 className="text-3xl font-bold mb-6 gradient-text">
                  Ready to Build Something Amazing?
                </h2>
                <p className="text-terminal-text mb-8">
                  Let's discuss your project and bring your ideas to life with
                  modern, scalable solutions.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button
                    className="bg-terminal-cyan text-terminal-bg hover:bg-terminal-cyan/80 font-bold px-8 py-3"
                    onClick={() => window.open("tel:+919801334862")}
                  >
                    Call Me Now
                  </Button>
                  <Button
                    variant="outline"
                    className="border-terminal-cyan text-terminal-cyan hover:bg-terminal-cyan hover:text-terminal-bg px-8 py-3"
                    onClick={() =>
                      window.open(
                        "https://calendly.com/pritish9801-edu/30min",
                        "_blank"
                      )
                    }
                  >
                    <Calendar className="w-4 h-4 mr-2" />
                    Schedule a Call
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <ContactForm />

      {/* Footer */}
      <footer className="py-12 border-t border-terminal-border">
        <div className="container mx-auto px-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-terminal-text mb-4 md:mb-0">
              © 2025 Pritish Biswas. Crafted with 💙 and 🧑‍💻.
            </div>
            <div className="flex space-x-6">
              <a
                href="https://github.com/PritBlitz"
                className="text-terminal-text hover:text-terminal-cyan transition-colors"
              >
                <Github className="w-6 h-6" />
              </a>
              <a
                href="mailto:<EMAIL>"
                className="text-terminal-text hover:text-terminal-cyan transition-colors"
              >
                <Mail className="w-6 h-6" />
              </a>
              <a
                href="https://maps.app.goo.gl/FXGSdjtFkZawxSTq5"
                className="text-terminal-text hover:text-terminal-cyan transition-colors"
              >
                <MapPin className="w-6 h-6" />
              </a>
              <a
                href="https://www.linkedin.com/in/pritish-biswas-pb24/"
                className="text-terminal-text hover:text-terminal-cyan transition-colors"
              >
                <Linkedin className="w-6 h-6" />
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
