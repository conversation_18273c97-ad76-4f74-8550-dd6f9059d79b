import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Gith<PERSON>, ExternalLink, ArrowDown, Linkedin } from "lucide-react";

const HeroSection = () => {
  const [displayText, setDisplayText] = useState("");
  const [currentLine, setCurrentLine] = useState(0);
  const [showCursor, setShowCursor] = useState(true);

  const terminalLines = [
    "$ whoami",
    " pritish-biswas",
    "$ cat roles.txt",
    " Full Stack Web Developer",
    " DevOps & Cloud Enthusiast",
    " System Architect",
    '$ echo "Building scalable solutions..."',
    " Building scalable solutions...",
    "$ status",
    " Available for new projects ✨",
  ];

  useEffect(() => {
    if (currentLine < terminalLines.length) {
      const text = terminalLines[currentLine];
      let i = 0;

      const typeWriter = setInterval(() => {
        if (i < text.length) {
          setDisplayText((prev) => prev + text.charAt(i));
          i++;
        } else {
          clearInterval(typeWriter);
          setTimeout(() => {
            setDisplayText((prev) => prev + "\n");
            setCurrentLine((prev) => prev + 1);
          }, 1000);
        }
      }, 50);

      return () => clearInterval(typeWriter);
    }
  }, [currentLine]);

  useEffect(() => {
    const cursorInterval = setInterval(() => {
      setShowCursor((prev) => !prev);
    }, 500);

    return () => clearInterval(cursorInterval);
  }, []);

  return (
    <section className="min-h-screen flex items-center justify-center relative pt-20">
      <div className="container mx-auto px-6">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Terminal Window */}
          <div className="order-2 lg:order-1">
            <div className="glass-effect rounded-lg overflow-hidden">
              {/* Terminal Header */}
              <div className="bg-terminal-border px-4 py-3 flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                <div className="ml-4 text-terminal-text text-sm">
                  pritish@dev:~
                </div>
              </div>

              {/* Terminal Content */}
              <div className="p-6 h-80 overflow-hidden">
                <pre className="text-terminal-green font-mono text-sm leading-relaxed whitespace-pre-wrap">
                  {displayText}
                  <span
                    className={`${
                      showCursor ? "opacity-100" : "opacity-0"
                    } transition-opacity`}
                  >
                    ▊
                  </span>
                </pre>
              </div>
            </div>
          </div>

          {/* Hero Content */}
          <div className="order-1 lg:order-2 text-center lg:text-left">
            <div className="mb-6">
              <h1 className="text-5xl lg:text-7xl font-bold mb-4">
                <span className="text-terminal-text">Hi, I'm </span>
                <span className="gradient-text animate-glow">Pritish</span>
              </h1>
              <div className="text-xl lg:text-2xl text-terminal-cyan mb-4 font-mono">
                &gt; Full Stack Developer
              </div>
              <div className="text-lg text-terminal-text mb-6">
                Crafting scalable web solutions with modern tech stacks.
                <br />
                Passionate about DevOps, Cloud Infrastructure, and Clean Code.
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-8">
              <Button
                size="lg"
                className="bg-terminal-cyan text-terminal-bg hover:bg-terminal-cyan/80 font-bold px-8 py-3 group"
                onClick={() =>
                  window.open(
                    "https://drive.google.com/file/d/1Yz2KH8y1oOyCNwPYvOqCqLF060XS2PD1/view?usp=sharing",
                    "_blank"
                  )
                }
              >
                Grab My Resume
                <ExternalLink className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="border-terminal-green text-terminal-green hover:bg-terminal-green hover:text-terminal-bg px-8 py-3"
                onClick={() =>
                  window.open("https://github.com/PritBlitz", "_blank")
                }
              >
                <Github className="mr-2 w-4 h-4" />
                GitHub Profile
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-8 py-3"
                onClick={() =>
                  window.open(
                    "https://www.linkedin.com/in/pritish-biswas-pb24/",
                    "_blank"
                  )
                }
              >
                <Linkedin className="mr-2 w-4 h-4" />
                LinkedIn Profile
              </Button>
            </div>

            {/* Status Indicators */}
            <div className="flex flex-wrap gap-4 justify-center lg:justify-start">
              <div className="flex items-center space-x-2 glass-effect px-4 py-2 rounded-full">
                <div className="w-2 h-2 bg-terminal-green rounded-full animate-pulse"></div>
                <span className="text-sm text-terminal-text">
                  Available for projects
                </span>
              </div>
              <div className="flex items-center space-x-2 glass-effect px-4 py-2 rounded-full">
                <div className="w-2 h-2 bg-terminal-cyan rounded-full animate-pulse"></div>
                <span className="text-sm text-terminal-text">
                  Remote worldwide
                </span>
              </div>
              <div className="flex items-center space-x-2 glass-effect px-4 py-2 rounded-full">
                <div className="w-2 h-2 bg-terminal-green rounded-full animate-pulse"></div>
                <span className="text-sm text-terminal-text">
                  Available for Internships
                </span>
              </div>
              <div className="flex items-center space-x-2 glass-effect px-4 py-2 rounded-full">
                <div className="w-2 h-2 bg-terminal-purple rounded-full animate-pulse"></div>
                <span className="text-sm text-terminal-text">
                  Available for Hackathon Collaborations
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <ArrowDown className="w-6 h-6 text-terminal-cyan" />
        </div>
      </div>

      {/* Floating Elements */}
      <div className="absolute top-20 right-10 w-20 h-20 border border-terminal-cyan/30 rounded-full animate-float"></div>
      <div
        className="absolute bottom-20 left-10 w-16 h-16 border border-terminal-purple/30 rounded-full animate-float"
        style={{ animationDelay: "1s" }}
      ></div>
    </section>
  );
};

export default HeroSection;
