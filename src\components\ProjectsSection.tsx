import { <PERSON>, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Github, ExternalLink, Calendar } from "lucide-react";

const ProjectsSection = () => {
  const projects = [
    {
      title: "EvolveX",
      description:
        "EvolveX is an AI-powered platform that automates video content workflows and delivers actionable insights to help creators optimize engagement and strategy.",
      image: "/evolvex.png",
      technologies: [
        "React",
        "Next Js",
        "Tailwind CSS",
        "OpenAI",
        "ConvexDB",
        "Youtube.js",
        "<PERSON> Auth",
        "Schematic",
        "Stripe",
      ],
      github: "https://github.com/PritBlitz/EvolveX",
      live: "https://evolve-x.up.railway.app/",
      featured: true,
    },
    {
      title: "Mamta.AI",
      description:
        "Mamta.AI is an AI-powered platform offering 24x7 emotional support, health guidance, and real-time SOS features for women in a secure and private digital space.",
      image: "/mamta.png",
      technologies: [
        "Next Js",
        "Tailwind CSS",
        "Gemini Flash 2.O",
        "Vertex AI",
        "Google Cloud",
        "Clerk Auth",
        "OpenStreetMaps",
        "WhatsApp API",
      ],
      github: "https://github.com/PritBlitz/Mamta.AI",
      live: "https://mamta-ai.vercel.app/",
      featured: true,
    },
    {
      title: "Real-time Chat Application",
      description:
        "Scalable chat app with WebSocket connections, file sharing, and group management. Built with modern React and Node.js.",
      image: "/placeholder.svg",
      technologies: ["React", "Socket.io", "Node.js", "Redis", "MongoDB"],
      github: "https://github.com/pritish",
      live: "https://demo.example.com",
      featured: false,
    },
    {
      title: "Cloud Infrastructure Automation",
      description:
        "Terraform modules for AWS infrastructure provisioning with automatic scaling, monitoring, and backup strategies.",
      image: "/placeholder.svg",
      technologies: ["Terraform", "AWS", "Python", "CloudFormation", "Lambda"],
      github: "https://github.com/pritish",
      live: null,
      featured: false,
    },

    {
      title: "Microservices Architecture",
      description:
        "Distributed system with API Gateway, service discovery, and event-driven communication. Deployed on Kubernetes.",
      image: "/placeholder.svg",
      technologies: ["Docker", "Kubernetes", "Node.js", "RabbitMQ", "MongoDB"],
      github: "https://github.com/pritish",
      live: "https://demo.example.com",
      featured: false,
    },
  ];

  const featuredProjects = projects.filter((p) => p.featured);
  const otherProjects = projects.filter((p) => !p.featured);

  return (
    <section id="projects" className="py-24 relative">
      <div className="container mx-auto px-6">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-4xl font-bold mb-4 text-center gradient-text">
            $ cat projects.json
          </h2>
          <p className="text-center text-terminal-text mb-12">
            Featured projects showcasing my full-stack and DevOps expertise
          </p>

          {/* Featured Projects */}
          <div className="mb-16">
            <h3 className="text-2xl font-bold text-terminal-cyan mb-8 flex items-center">
              <span className="text-terminal-green mr-2">//</span>
              Featured Projects
            </h3>
            <div className="grid lg:grid-cols-2 gap-8">
              {featuredProjects.map((project, index) => (
                <Card
                  key={project.title}
                  className="glass-effect border-terminal-border hover:neon-border transition-all duration-300 group overflow-hidden"
                >
                  <div className="aspect-video bg-terminal-border relative overflow-hidden">
                    <img
                      src={project.image}
                      alt={project.title}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 bg-terminal-bg/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                      <div className="flex space-x-4">
                        <Button
                          size="sm"
                          className="bg-terminal-cyan text-terminal-bg hover:bg-terminal-cyan/80"
                          onClick={() => window.open(project.github, "_blank")}
                        >
                          <Github className="w-4 h-4 mr-2" />
                          Code
                        </Button>
                        {project.live && (
                          <Button
                            size="sm"
                            variant="outline"
                            className="border-terminal-green text-terminal-green hover:bg-terminal-green hover:text-terminal-bg"
                            onClick={() => window.open(project.live, "_blank")}
                          >
                            <ExternalLink className="w-4 h-4 mr-2" />
                            Live
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                  <CardContent className="p-6">
                    <h4 className="text-xl font-bold text-terminal-cyan mb-3">
                      {project.title}
                    </h4>
                    <p className="text-terminal-text mb-4 leading-relaxed">
                      {project.description}
                    </p>
                    <div className="flex flex-wrap gap-2 mb-4">
                      {project.technologies.map((tech) => (
                        <Badge
                          key={tech}
                          variant="secondary"
                          className="bg-terminal-border text-terminal-cyan text-xs"
                        >
                          {tech}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Other Projects Grid */}
          <div>
            <h3 className="text-2xl font-bold text-terminal-cyan mb-8 flex items-center">
              <span className="text-terminal-green mr-2">//</span>
              More Projects
            </h3>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {otherProjects.map((project) => (
                <Card
                  key={project.title}
                  className="glass-effect border-terminal-border hover:neon-border transition-all duration-300 group"
                >
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start mb-4">
                      <h4 className="text-lg font-bold text-terminal-cyan group-hover:text-white transition-colors">
                        {project.title}
                      </h4>
                      <div className="flex space-x-2">
                        <Button
                          size="sm"
                          variant="ghost"
                          className="p-1 h-auto text-terminal-text hover:text-terminal-cyan"
                          onClick={() => window.open(project.github, "_blank")}
                        >
                          <Github className="w-4 h-4" />
                        </Button>
                        {project.live && (
                          <Button
                            size="sm"
                            variant="ghost"
                            className="p-1 h-auto text-terminal-text hover:text-terminal-cyan"
                            onClick={() => window.open(project.live, "_blank")}
                          >
                            <ExternalLink className="w-4 h-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                    <p className="text-terminal-text text-sm mb-4 leading-relaxed">
                      {project.description}
                    </p>
                    <div className="flex flex-wrap gap-1">
                      {project.technologies.slice(0, 3).map((tech) => (
                        <Badge
                          key={tech}
                          variant="secondary"
                          className="bg-terminal-border text-terminal-cyan text-xs"
                        >
                          {tech}
                        </Badge>
                      ))}
                      {project.technologies.length > 3 && (
                        <Badge
                          variant="secondary"
                          className="bg-terminal-border text-terminal-text text-xs"
                        >
                          +{project.technologies.length - 3}
                        </Badge>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* View More Projects */}
          <div className="text-center mt-12">
            <Button
              variant="outline"
              size="lg"
              className="border-terminal-cyan text-terminal-cyan hover:bg-terminal-cyan hover:text-terminal-bg px-8 py-3"
              onClick={() =>
                window.open("https://github.com/pritish", "_blank")
              }
            >
              <Github className="w-4 h-4 mr-2" />
              View All Projects on GitHub
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProjectsSection;
